---
id: cloc-sdk
title: Cloc SDK
sidebar_label: Cloc SDK
sidebar_position: 2
---

# Ever Cloc SDK

Transform your productivity applications with the Ever Cloc SDK, a comprehensive development toolkit designed for building sophisticated time tracking and productivity solutions. Whether you're creating focused work timers, project management systems, or enterprise-grade productivity suites, our SDK provides the professional-grade building blocks you need.

## Introduction

The Ever Cloc SDK is a powerful, modular library that empowers developers to integrate enterprise-level time tracking capabilities into their applications. Built for modern JavaScript environments including Node.js, React, and Next.js, it offers a complete ecosystem of ready-to-use components, utilities, and services that seamlessly integrate into your existing development workflow.

:::tip Developer-First Design
Our SDK is built by developers, for developers. Every component is designed with developer experience in mind, featuring comprehensive TypeScript support, intuitive APIs, and extensive customization options.
:::

## Key Features

### **Flexible Timer Components**

- **Pre-built Interfaces**: Customizable timer components that adapt to your application's design system
- **Multiple Formats**: Support for countdown timers, stopwatches, Pomodoro techniques, and custom timing patterns
- **Real-time Sync**: Live synchronization capabilities across different views, devices, and user sessions
- **Responsive Design**: Mobile-first components that work seamlessly across all screen sizes

### **Developer-First Architecture**

- **Framework Agnostic**: Core functionality works with any JavaScript framework
- **Optimized Bindings**: Specialized implementations for React, Next.js, and vanilla JavaScript
- **Modular Design**: Import only the features you need to keep bundle sizes minimal
- **TypeScript Native**: Comprehensive type definitions with full IntelliSense support
- **Minimal Dependencies**: Lightweight architecture that doesn't bloat your application

### **Advanced Time Tracking**

- **High-Precision Measurement**: Microsecond-accurate time tracking for professional applications
- **Offline Capabilities**: Automatic synchronization when connectivity is restored
- **Event System**: Customizable webhooks and events for time-related triggers and notifications
- **Timezone Support**: Built-in handling for different time zones and international formats
- **Data Persistence**: Robust local storage with cloud backup capabilities

### **Enterprise-Ready Features**

- **Scalable Architecture**: Supports high-volume applications with thousands of concurrent users
- **Security First**: Built-in authentication, authorization, and data protection
- **Performance Optimized**: Efficient rendering and minimal resource consumption
- **Error Handling**: Comprehensive error recovery and graceful degradation
- **Monitoring & Analytics**: Built-in performance monitoring and usage analytics

## SDK Packages Overview

The Ever Cloc SDK consists of five core packages, each designed for specific functionality while working together as a cohesive ecosystem:

### **@cloc/atoms**

_React Components and UI Elements_

The atoms package provides a comprehensive library of React components for building time tracking interfaces.

**Key Features:**

- **Timer Components**: ModernCloc, BasicTimer, PomodoroTimer with multiple variants
- **Activity Displays**: Daily/Weekly activity displayers and worked time components
- **Form Components**: Registration, login, profile management, and team creation forms
- **Analytics Components**: Click insights, session analytics, and heatmap visualizations
- **Report Components**: Charts, graphs, and data visualization tools

**Use Cases:**

- Building custom time tracking dashboards
- Creating user authentication flows
- Implementing team management interfaces
- Adding analytics and reporting capabilities

### **@cloc/ui**

_Core UI Component Library_

A foundational UI library providing essential components and design system elements.

**Key Features:**

- **Base Components**: Buttons, inputs, modals, and navigation elements
- **Layout Components**: Grids, containers, and responsive layout utilities
- **Theme System**: Comprehensive theming with dark/light mode support
- **Accessibility**: WCAG-compliant components with full keyboard navigation
- **Design Tokens**: Consistent spacing, colors, and typography system

**Use Cases:**

- Building consistent user interfaces
- Implementing design systems
- Creating accessible applications
- Rapid prototyping and development

### **@cloc/types**

_TypeScript Type Definitions_

Comprehensive TypeScript definitions ensuring type safety across the entire SDK ecosystem.

**Key Features:**

- **API Types**: Complete type coverage for all API endpoints and responses
- **Component Props**: Fully typed component interfaces and prop definitions
- **Data Models**: Type-safe data structures for users, projects, tasks, and time entries
- **Configuration Types**: Typed configuration objects for SDK initialization
- **Event Types**: Type definitions for custom events and webhooks

**Use Cases:**

- Ensuring type safety in TypeScript projects
- Providing IntelliSense and autocomplete support
- Preventing runtime errors through compile-time checking
- Documenting API contracts and interfaces

### **@cloc/api**

_API Client and Integration Utilities_

A powerful API client providing seamless integration with Ever Cloc backend services.

**Key Features:**

- **RESTful Client**: Complete API client with automatic request/response handling
- **Authentication**: Built-in token management and refresh mechanisms
- **Caching**: Intelligent caching strategies for improved performance
- **Offline Support**: Queue management for offline operations
- **Error Handling**: Comprehensive error recovery and retry logic

**Use Cases:**

- Integrating with Ever Cloc backend services
- Building custom API integrations
- Implementing offline-first applications
- Creating data synchronization workflows

### **@cloc/tracking**

_Analytics and User Interaction Tracking_

Advanced analytics library built on Microsoft Clarity for comprehensive user behavior tracking.

**Key Features:**

- **Interaction Tracking**: Click, scroll, and navigation pattern analysis
- **Session Recording**: Complete session replay capabilities
- **Heatmap Generation**: Visual representation of user interaction patterns
- **Performance Monitoring**: Page load times and user experience metrics
- **Behavioral Analytics**: User journey analysis and engagement scoring

**Use Cases:**

- Understanding user behavior and application usage
- Optimizing user experience and interface design
- Monitoring application performance
- Generating insights for product development

## Benefits

### **Rapid Development**

Accelerate your development timeline by leveraging our battle-tested components and utilities. Focus on building your unique features while we handle the complexities of time tracking, user management, and data synchronization.

### **Enterprise-Ready**

Built with enterprise requirements in mind, our SDK supports high-volume applications with robust error handling, comprehensive security features, and performance optimization for demanding production environments.

### **Extensive Customization**

Take complete control over the visual and functional aspects of your implementation. From simple styling adjustments to complex behavioral modifications, every component and utility is designed for maximum flexibility.

### **Reliable Performance**

Engineered for stability and accuracy, our SDK ensures consistent time tracking and data management across different environments, devices, and use cases with comprehensive testing and quality assurance.

## Getting Started

### Installation

Install the packages you need for your project:

```bash
# Install all packages
npm install @cloc/atoms @cloc/ui @cloc/types @cloc/api @cloc/tracking

# Or install specific packages
npm install @cloc/atoms @cloc/ui
```

### Quick Start Example

```typescript
import { ClocProvider, ModernCloc } from '@cloc/atoms';
import { Button } from '@cloc/ui';
import { tracker } from '@cloc/tracking';

// Initialize tracking
tracker.start({
  organizationId: 'your-org-id',
  tenantId: 'your-tenant-id',
  token: 'your-auth-token'
});

function App() {
  return (
    <ClocProvider>
      <div className="app">
        <h1>My Time Tracking App</h1>
        <ModernCloc expanded showProgress />
        <Button variant="primary">Start Tracking</Button>
      </div>
    </ClocProvider>
  );
}
```

## Next Steps

:::info Explore the SDK

- **[Component Library](/docs/components)** - Browse all available React components
- **[API Reference](/docs/api-reference)** - Complete API documentation and examples
- **[Tracking Guide](/docs/tracking-package)** - Implement advanced analytics and tracking
- **[Examples](/docs/examples)** - Real-world implementation examples and tutorials
- **[Migration Guide](/docs/migration)** - Upgrade from previous versions
  :::

### Community & Resources

- **[Storybook](https://storybook.cloc.ai)** - Interactive component documentation
- **[GitHub Repository](https://github.com/ever-co/ever-cloc)** - Source code and issue tracking
- **[Community Chat](https://gitter.im/ever-co/ever-cloc)** - Connect with other developers
- **[Blog](https://cloc.ai/blog)** - Latest updates and development insights

---

**Ready to build amazing productivity tools?** Start exploring the Ever Cloc SDK today and transform how your users track time and manage productivity.
