{"name": "@cloc/api", "version": "0.1.8", "main": "dist/index.es.js", "types": "dist/index.d.ts", "type": "module", "license": "MIT", "files": ["dist"], "publishConfig": {"access": "restricted"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.es.js"}}, "scripts": {"build": " rollup -c"}, "dependencies": {"@cloc/types": "*"}, "devDependencies": {"@cloc/typescript-config": "*", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.1", "rollup": "^4.25.0", "typescript": "^5.6.3"}}